import type { RuleConfig } from '../rule-config';

/**
 * Option.
 */
export interface NoEmptyFunctionOption {
  allow?: (
    | 'functions'
    | 'arrowFunctions'
    | 'generatorFunctions'
    | 'methods'
    | 'generatorMethods'
    | 'getters'
    | 'setters'
    | 'constructors'
    | 'asyncFunctions'
    | 'asyncMethods'
  )[];
}

/**
 * Options.
 */
export type NoEmptyFunctionOptions = [NoEmptyFunctionOption?];

/**
 * Disallow empty functions.
 *
 * @see [no-empty-function](https://eslint.org/docs/latest/rules/no-empty-function)
 */
export type NoEmptyFunctionRuleConfig = RuleConfig<NoEmptyFunctionOptions>;

/**
 * Disallow empty functions.
 *
 * @see [no-empty-function](https://eslint.org/docs/latest/rules/no-empty-function)
 */
export interface NoEmptyFunctionRule {
  /**
   * Disallow empty functions.
   *
   * @see [no-empty-function](https://eslint.org/docs/latest/rules/no-empty-function)
   */
  'no-empty-function': NoEmptyFunctionRuleConfig;
}
