import type { RuleConfig } from '../rule-config';

/**
 * Option.
 */
export interface KeywordSpacingOption {
  before?: boolean;
  after?: boolean;
  overrides?: {
    abstract?: {
      before?: boolean;
      after?: boolean;
    };
    as?: {
      before?: boolean;
      after?: boolean;
    };
    async?: {
      before?: boolean;
      after?: boolean;
    };
    await?: {
      before?: boolean;
      after?: boolean;
    };
    boolean?: {
      before?: boolean;
      after?: boolean;
    };
    break?: {
      before?: boolean;
      after?: boolean;
    };
    byte?: {
      before?: boolean;
      after?: boolean;
    };
    case?: {
      before?: boolean;
      after?: boolean;
    };
    catch?: {
      before?: boolean;
      after?: boolean;
    };
    char?: {
      before?: boolean;
      after?: boolean;
    };
    class?: {
      before?: boolean;
      after?: boolean;
    };
    const?: {
      before?: boolean;
      after?: boolean;
    };
    continue?: {
      before?: boolean;
      after?: boolean;
    };
    debugger?: {
      before?: boolean;
      after?: boolean;
    };
    default?: {
      before?: boolean;
      after?: boolean;
    };
    delete?: {
      before?: boolean;
      after?: boolean;
    };
    do?: {
      before?: boolean;
      after?: boolean;
    };
    double?: {
      before?: boolean;
      after?: boolean;
    };
    else?: {
      before?: boolean;
      after?: boolean;
    };
    enum?: {
      before?: boolean;
      after?: boolean;
    };
    export?: {
      before?: boolean;
      after?: boolean;
    };
    extends?: {
      before?: boolean;
      after?: boolean;
    };
    false?: {
      before?: boolean;
      after?: boolean;
    };
    final?: {
      before?: boolean;
      after?: boolean;
    };
    finally?: {
      before?: boolean;
      after?: boolean;
    };
    float?: {
      before?: boolean;
      after?: boolean;
    };
    for?: {
      before?: boolean;
      after?: boolean;
    };
    from?: {
      before?: boolean;
      after?: boolean;
    };
    function?: {
      before?: boolean;
      after?: boolean;
    };
    get?: {
      before?: boolean;
      after?: boolean;
    };
    goto?: {
      before?: boolean;
      after?: boolean;
    };
    if?: {
      before?: boolean;
      after?: boolean;
    };
    implements?: {
      before?: boolean;
      after?: boolean;
    };
    import?: {
      before?: boolean;
      after?: boolean;
    };
    in?: {
      before?: boolean;
      after?: boolean;
    };
    instanceof?: {
      before?: boolean;
      after?: boolean;
    };
    int?: {
      before?: boolean;
      after?: boolean;
    };
    interface?: {
      before?: boolean;
      after?: boolean;
    };
    let?: {
      before?: boolean;
      after?: boolean;
    };
    long?: {
      before?: boolean;
      after?: boolean;
    };
    native?: {
      before?: boolean;
      after?: boolean;
    };
    new?: {
      before?: boolean;
      after?: boolean;
    };
    null?: {
      before?: boolean;
      after?: boolean;
    };
    of?: {
      before?: boolean;
      after?: boolean;
    };
    package?: {
      before?: boolean;
      after?: boolean;
    };
    private?: {
      before?: boolean;
      after?: boolean;
    };
    protected?: {
      before?: boolean;
      after?: boolean;
    };
    public?: {
      before?: boolean;
      after?: boolean;
    };
    return?: {
      before?: boolean;
      after?: boolean;
    };
    set?: {
      before?: boolean;
      after?: boolean;
    };
    short?: {
      before?: boolean;
      after?: boolean;
    };
    static?: {
      before?: boolean;
      after?: boolean;
    };
    super?: {
      before?: boolean;
      after?: boolean;
    };
    switch?: {
      before?: boolean;
      after?: boolean;
    };
    synchronized?: {
      before?: boolean;
      after?: boolean;
    };
    this?: {
      before?: boolean;
      after?: boolean;
    };
    throw?: {
      before?: boolean;
      after?: boolean;
    };
    throws?: {
      before?: boolean;
      after?: boolean;
    };
    transient?: {
      before?: boolean;
      after?: boolean;
    };
    true?: {
      before?: boolean;
      after?: boolean;
    };
    try?: {
      before?: boolean;
      after?: boolean;
    };
    typeof?: {
      before?: boolean;
      after?: boolean;
    };
    var?: {
      before?: boolean;
      after?: boolean;
    };
    void?: {
      before?: boolean;
      after?: boolean;
    };
    volatile?: {
      before?: boolean;
      after?: boolean;
    };
    while?: {
      before?: boolean;
      after?: boolean;
    };
    with?: {
      before?: boolean;
      after?: boolean;
    };
    yield?: {
      before?: boolean;
      after?: boolean;
    };
  };
}

/**
 * Options.
 */
export type KeywordSpacingOptions = [KeywordSpacingOption?];

/**
 * Enforce consistent spacing before and after keywords.
 *
 * @see [keyword-spacing](https://eslint.org/docs/latest/rules/keyword-spacing)
 */
export type KeywordSpacingRuleConfig = RuleConfig<KeywordSpacingOptions>;

/**
 * Enforce consistent spacing before and after keywords.
 *
 * @see [keyword-spacing](https://eslint.org/docs/latest/rules/keyword-spacing)
 */
export interface KeywordSpacingRule {
  /**
   * Enforce consistent spacing before and after keywords.
   *
   * @see [keyword-spacing](https://eslint.org/docs/latest/rules/keyword-spacing)
   */
  'keyword-spacing': KeywordSpacingRuleConfig;
}
