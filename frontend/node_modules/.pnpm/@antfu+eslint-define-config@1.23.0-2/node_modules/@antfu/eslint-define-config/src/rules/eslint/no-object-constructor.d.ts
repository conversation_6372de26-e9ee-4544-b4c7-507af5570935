import type { RuleConfig } from '../rule-config';

/**
 * <PERSON><PERSON><PERSON> calls to the `Object` constructor without an argument.
 *
 * @see [no-object-constructor](https://eslint.org/docs/latest/rules/no-object-constructor)
 */
export type NoObjectConstructorRuleConfig = RuleConfig<[]>;

/**
 * <PERSON><PERSON><PERSON> calls to the `Object` constructor without an argument.
 *
 * @see [no-object-constructor](https://eslint.org/docs/latest/rules/no-object-constructor)
 */
export interface NoObjectConstructorRule {
  /**
   * Di<PERSON><PERSON> calls to the `Object` constructor without an argument.
   *
   * @see [no-object-constructor](https://eslint.org/docs/latest/rules/no-object-constructor)
   */
  'no-object-constructor': NoObjectConstructorRuleConfig;
}
