{"name": "@alova/mock", "version": "2.0.17", "description": "mock request adapter for alova.js", "homepage": "https://github.com/alovajs/mock", "main": "dist/alova-mock.common.cjs", "module": "dist/alova-mock.esm.js", "types": "typings/index.d.ts", "type": "module", "jsdelivr": "dist/alova-mock.umd.min.js", "unpkg": "dist/alova-mock.umd.min.js", "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/alovajs/alova.git"}, "bugs": {"url": "https://github.com/alovajs/alova/issues"}, "files": ["dist", "typings/*.d.ts"], "peerDependencies": {"alova": "^3.0.20"}, "devDependencies": {"alova": "3.3.3", "@alova/scripts": "1.1.1"}, "dependencies": {"@alova/shared": "1.3.1"}, "scripts": {"clean": "rimraf ./dist", "test": "vitest run", "lint": "eslint --ext .ts,.js src/**", "lint:fix": "eslint --ext .ts,.js src/** --fix", "build": "npm run clean && alova-scripts build", "release": "semantic-release", "coveralls": "npm run test:coverage && coveralls < coverage/lcov.info", "commit": "git add . && git-cz && git push"}}