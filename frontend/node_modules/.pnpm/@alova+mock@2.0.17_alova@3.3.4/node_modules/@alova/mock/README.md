<img width="100%" src="https://alova.js.org/img/cover.jpg" />

# mock adpater for alova

Refer to [alova](https://github.com/alovajs/alova).

## Usage

Please to [adapter mock documentation](https://alova.js.org/resource/request-adapter/alova-mock) for more details.

## Join the community

- [Follow us on X to get the latest updates](https://x.com/alovajs)

- [Join the Discord](https://discord.gg/S47QGJgkVb)

- [Join the WeChat group](https://alova.js.org/img/wechat_qrcode.jpg)

## We need your support

If you like alova, we are very grateful for giving us a star in the upper right corner, which is a recognition and encouragement for our work.

## Welcome to contribute

We are honored to receive active participation from developers around the world in Issues and Discussions.

We hope to make alova a common project for everyone who is willing to participate. We encourage everyone to become a contributor to the alova community with an open and inclusive attitude. Even if you are a junior developer, as long as your ideas meet the development guidelines of alova, please participate generously.

Effective contributions will win you a certain reputation in the Alova community. Before contributing, please be sure to read the [Contribution Guide](https://github.com/alovajs/alova/blob/main/CONTRIBUTING.md) in detail to ensure your contribution is effective.

## Changelog

[Link](https://github.com/alovajs/alova/releases)

## Contributors

<a href="https://github.com/alovajs/alova/graphs/contributors">
<img src="https://contrib.rocks/image?repo=alovajs/alova&max=30&columns=10" />
</a>

## LICENSE

[MIT](https://en.wikipedia.org/wiki/MIT_License)
