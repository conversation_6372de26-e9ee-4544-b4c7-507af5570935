#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.34.0_jiti@2.5.1_/node_modules/eslint-config-prettier/bin/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.34.0_jiti@2.5.1_/node_modules/eslint-config-prettier/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.34.0_jiti@2.5.1_/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.34.0_jiti@2.5.1_/node_modules/eslint-config-prettier/bin/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.34.0_jiti@2.5.1_/node_modules/eslint-config-prettier/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/eslint-config-prettier@10.1.5_eslint@9.34.0_jiti@2.5.1_/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../eslint-config-prettier/bin/cli.js" "$@"
else
  exec node  "$basedir/../eslint-config-prettier/bin/cli.js" "$@"
fi
