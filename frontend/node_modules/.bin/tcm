#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/typed-css-modules@0.9.1/node_modules/typed-css-modules/lib/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/typed-css-modules@0.9.1/node_modules/typed-css-modules/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/typed-css-modules@0.9.1/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/typed-css-modules@0.9.1/node_modules/typed-css-modules/lib/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/typed-css-modules@0.9.1/node_modules/typed-css-modules/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/typed-css-modules@0.9.1/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../typed-css-modules/lib/cli.js" "$@"
else
  exec node  "$basedir/../typed-css-modules/lib/cli.js" "$@"
fi
