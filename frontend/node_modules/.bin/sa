#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/repos/ignite-soybean/frontend/packages/scripts/node_modules:/home/<USER>/repos/ignite-soybean/frontend/packages/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules:/home/<USER>/repos/ignite-soybean/node_modules:/home/<USER>/repos/node_modules:/home/<USER>/node_modules:/home/<USER>/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/repos/ignite-soybean/frontend/packages/scripts/node_modules:/home/<USER>/repos/ignite-soybean/frontend/packages/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules:/home/<USER>/repos/ignite-soybean/node_modules:/home/<USER>/repos/node_modules:/home/<USER>/node_modules:/home/<USER>/node_modules:/home/<USER>/repos/ignite-soybean/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/tsx" ]; then
  exec "$basedir/tsx"  "$basedir/../@sa/scripts/bin.ts" "$@"
else
  exec tsx  "$basedir/../@sa/scripts/bin.ts" "$@"
fi
